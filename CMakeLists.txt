cmake_minimum_required(VERSION 3.15)

project(PixivTagDownloader 
    VERSION 1.0.0
    DESCRIPTION "A C++ application for downloading Pixiv artworks by user ID and tags"
    LANGUAGES CXX
)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Set default build type
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Compiler-specific options
if(MSVC)
    add_compile_options(/W4 /utf-8)
    add_definitions(-D_WIN32_WINNT=0x0601)
else()
    add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# Find required packages
find_package(Threads REQUIRED)

# Try to find system packages first, fallback to manual inclusion
find_package(PkgConfig QUIET)

# Find nlohmann/json
find_path(NLOHMANN_JSON_INCLUDE_DIR nlohmann/json.hpp
    PATHS /usr/include /usr/local/include
)

# Find yaml-cpp
find_library(YAML_CPP_LIBRARY yaml-cpp
    PATHS /usr/lib /usr/local/lib /usr/lib/x86_64-linux-gnu
)
find_path(YAML_CPP_INCLUDE_DIR yaml-cpp/yaml.h
    PATHS /usr/include /usr/local/include
)

# Find spdlog
find_library(SPDLOG_LIBRARY spdlog
    PATHS /usr/lib /usr/local/lib /usr/lib/x86_64-linux-gnu
)
find_path(SPDLOG_INCLUDE_DIR spdlog/spdlog.h
    PATHS /usr/include /usr/local/include
)

# Find fmt (required by spdlog)
find_library(FMT_LIBRARY fmt
    PATHS /usr/lib /usr/local/lib /usr/lib/x86_64-linux-gnu
)

# Create interface libraries for found packages
if(NLOHMANN_JSON_INCLUDE_DIR)
    add_library(nlohmann_json INTERFACE)
    target_include_directories(nlohmann_json INTERFACE ${NLOHMANN_JSON_INCLUDE_DIR})
    message(STATUS "Found nlohmann/json: ${NLOHMANN_JSON_INCLUDE_DIR}")
else()
    message(WARNING "nlohmann/json not found, using single header fallback")
    # Create a simple fallback
    add_library(nlohmann_json INTERFACE)
endif()

if(YAML_CPP_LIBRARY AND YAML_CPP_INCLUDE_DIR)
    add_library(yaml-cpp INTERFACE)
    target_link_libraries(yaml-cpp INTERFACE ${YAML_CPP_LIBRARY})
    target_include_directories(yaml-cpp INTERFACE ${YAML_CPP_INCLUDE_DIR})
    message(STATUS "Found yaml-cpp: ${YAML_CPP_LIBRARY}")
else()
    message(WARNING "yaml-cpp not found")
    add_library(yaml-cpp INTERFACE)
endif()

if(SPDLOG_LIBRARY AND SPDLOG_INCLUDE_DIR)
    add_library(spdlog INTERFACE)
    target_link_libraries(spdlog INTERFACE ${SPDLOG_LIBRARY})
    target_include_directories(spdlog INTERFACE ${SPDLOG_INCLUDE_DIR})
    if(FMT_LIBRARY)
        target_link_libraries(spdlog INTERFACE ${FMT_LIBRARY})
        message(STATUS "Found spdlog: ${SPDLOG_LIBRARY} with fmt: ${FMT_LIBRARY}")
    else()
        message(STATUS "Found spdlog: ${SPDLOG_LIBRARY}")
    endif()
else()
    message(WARNING "spdlog not found")
    add_library(spdlog INTERFACE)
endif()

# Create simple CLI11 and httplib interfaces (header-only libraries)
add_library(CLI11 INTERFACE)
add_library(httplib INTERFACE)

# Include directories
include_directories(${CMAKE_SOURCE_DIR}/include)

# Source files
file(GLOB_RECURSE SOURCES 
    "src/*.cpp"
    "src/*.h"
)

# Create executable
add_executable(${PROJECT_NAME} ${SOURCES})

# Link libraries
target_link_libraries(${PROJECT_NAME}
    PRIVATE
    nlohmann_json
    yaml-cpp
    spdlog
    CLI11
    httplib
    Threads::Threads
)

# Platform-specific libraries
if(WIN32)
    target_link_libraries(${PROJECT_NAME} PRIVATE ws2_32 wsock32)
endif()

# Install target
install(TARGETS ${PROJECT_NAME}
    RUNTIME DESTINATION bin
)

# CPack configuration
include(CPack)
set(CPACK_PACKAGE_NAME "PixivTagDownloader")
set(CPACK_PACKAGE_VERSION ${PROJECT_VERSION})
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY ${PROJECT_DESCRIPTION})
set(CPACK_PACKAGE_VENDOR "Mannix Sun")
set(CPACK_PACKAGE_CONTACT "<EMAIL>")
