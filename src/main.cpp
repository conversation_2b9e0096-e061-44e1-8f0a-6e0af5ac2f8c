/**
 * @file main.cpp
 * @brief Main entry point for PixivTagDownloader
 * <AUTHOR> Sun <<EMAIL>>
 * @version 1.0.0
 * 
 * PixivTagDownloader - A C++ application for downloading Pixiv artworks by user ID and tags
 * 
 * This program allows users to download artworks from Pixiv based on user ID and tag filters.
 * It supports both interactive and command-line modes, with configurable download methods
 * including direct download, aria2c, and aria2 RPC.
 * 
 * Features:
 * - Cookie-based authentication
 * - Tag filtering with AND/OR/NOT logic
 * - Multiple artwork types (Illust, Manga, Novel)
 * - Configurable directory structure and file naming
 * - Concurrent downloads with rate limiting
 * - Progress tracking and logging
 * - Cross-platform support
 * 
 * Usage:
 *   Interactive mode: ./PixivTagDownloader
 *   Command line mode: ./PixivTagDownloader -u <uid> -t <tags> [options]
 * 
 * Configuration:
 *   Copy config.yaml.example to config.yaml and modify as needed.
 *   The configuration file must contain a valid Pixiv session cookie.
 * 
 * License: MIT License
 * Copyright (c) 2024 Mannix Sun
 */

#include "core_logic.h"
#include <iostream>
#include <exception>

/**
 * @brief Application banner
 */
void print_banner() {
    std::cout << R"(
 ____  _       _       _____             ____                      _                 _           
|  _ \(_)_  __(_)_   _|_   _|_ _  __ _   |  _ \  _____      ___ __ | | ___   __ _  __| | ___ _ __ 
| |_) | \ \/ /| \ \ / / | |/ _` |/ _` |  | | | |/ _ \ \ /\ / / '_ \| |/ _ \ / _` |/ _` |/ _ \ '__|
|  __/| |>  < | |\ V /  | | (_| | (_| |  | |_| | (_) \ V  V /| | | | | (_) | (_| | (_| |  __/ |   
|_|   |_/_/\_\|_| \_/   |_|\__,_|\__, |  |____/ \___/ \_/\_/ |_| |_|_|\___/ \__,_|\__,_|\___|_|   
                                 |___/                                                            

PixivTagDownloader v1.0.0
A C++ application for downloading Pixiv artworks by user ID and tags
Author: Mannix Sun <<EMAIL>>

)" << std::endl;
}

/**
 * @brief Print usage information
 */
void print_usage() {
    std::cout << R"(
Usage:
  Interactive mode:
    PixivTagDownloader

  Command line mode:
    PixivTagDownloader -u <uid> [options]

Options:
  -u, --uid <UID>                 Target Pixiv user ID (required)
  -t, --tags <TAG1,TAG2,...>      Tags to filter by (comma-separated)
  -l, --logic <and|or|not>        Tag filtering logic (default: or)
  --all                           Download all artworks (ignore tag filter)
  -T, --type <TYPE1,TYPE2,...>    Artwork types: Illust,Manga,Novel,all (default: all)
  -c, --config <PATH>             Configuration file path (default: config.yaml)
  --output-dir <PATH>             Output directory (default: Output)
  --download-method <METHOD>      Download method: direct,aria2c,aria2rpc (default: direct)
  --aria2rpc-url <URL>            Aria2 RPC URL (default: ws://localhost:6800/jsonrpc)
  --aria2rpc-secret <SECRET>      Aria2 RPC secret (optional)
  --threads <NUM>                 Number of concurrent downloads (default: CPU cores)
  --delay <MIN-MAX>               Random delay range in seconds (default: 1-3)
  --skip-existing                 Skip existing files
  --overwrite-existing            Overwrite existing files
  --rename-existing               Rename new files to avoid conflicts
  --log-level <LEVEL>             Log level: trace,debug,info,warn,error (default: info)
  -h, --help                      Show this help message

Examples:
  # Interactive mode
  PixivTagDownloader

  # Download all artworks from user 123456
  PixivTagDownloader -u 123456 --all

  # Download artworks with specific tags
  PixivTagDownloader -u 123456 -t "original,illustration" -l and

  # Download only illustrations and manga
  PixivTagDownloader -u 123456 -T "Illust,Manga"

  # Use aria2 RPC for downloading
  PixivTagDownloader -u 123456 --download-method aria2rpc --aria2rpc-url ws://localhost:6800/jsonrpc

Configuration:
  Copy config.yaml.example to config.yaml and set your Pixiv session cookie.
  The cookie is required for authentication and accessing artworks.

)" << std::endl;
}

/**
 * @brief Main application entry point
 * @param argc Argument count
 * @param argv Argument values
 * @return Exit code (0 for success, non-zero for error)
 */
int main(int argc, char* argv[]) {
    try {
        // Print banner
        print_banner();
        
        // Check for help flag
        for (int i = 1; i < argc; ++i) {
            std::string arg = argv[i];
            if (arg == "-h" || arg == "--help") {
                print_usage();
                return 0;
            }
        }
        
        // Run application
        return pixiv::ApplicationRunner::run(argc, argv);
        
    } catch (const std::exception& e) {
        std::cerr << "Fatal error: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "Unknown fatal error occurred" << std::endl;
        return 1;
    }
}
