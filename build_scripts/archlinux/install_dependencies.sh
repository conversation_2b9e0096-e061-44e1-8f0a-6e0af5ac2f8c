#!/bin/bash

# PixivTagDownloader - Arch Linux Dependencies Installation Script
# This script installs all required dependencies for building PixivTagDownloader on Arch Linux

set -e

echo "Installing PixivTagDownloader dependencies for Arch Linux..."

# Install build tools and libraries
echo "Installing build tools and libraries..."
sudo pacman -S --needed --noconfirm \
    base-devel \
    cmake \
    git \
    nlohmann-json \
    yaml-cpp \
    spdlog \
    fmt

echo "All dependencies installed successfully!"
echo ""
echo "You can now build the project with:"
echo "  mkdir build && cd build"
echo "  cmake .."
echo "  make -j\$(nproc)"
