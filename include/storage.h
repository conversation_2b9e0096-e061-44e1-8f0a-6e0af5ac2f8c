#pragma once

#include "types.h"
#include <string>
#include <vector>

namespace pixiv {

/**
 * @brief Storage manager for organizing downloaded files
 * 
 * This class handles file organization, directory creation, and metadata generation
 * according to user-configured templates and naming conventions.
 */
class StorageManager {
public:
    /**
     * @brief Constructor
     * @param output_dir Base output directory
     * @param path_template Path template configuration
     * @param conflict_strategy File conflict resolution strategy
     */
    StorageManager(const std::string& output_dir,
                  const PathTemplate& path_template,
                  ConflictStrategy conflict_strategy);

    /**
     * @brief Destructor
     */
    ~StorageManager();

    /**
     * @brief Generate file path for artwork
     * @param artwork Artwork information
     * @param page_index Page index for multi-page artworks (-1 for single page)
     * @param is_metadata True if this is for metadata file
     * @return Generated file path
     */
    std::string generate_file_path(const ArtworkInfo& artwork, 
                                  int page_index = -1,
                                  bool is_metadata = false);

    /**
     * @brief Generate directory path for artwork
     * @param artwork Artwork information
     * @return Generated directory path
     */
    std::string generate_directory_path(const ArtworkInfo& artwork);

    /**
     * @brief Prepare directory for file
     * Creates necessary directories if they don't exist
     * @param file_path File path
     * @return true if directory preparation successful
     */
    bool prepare_directory(const std::string& file_path);

    /**
     * @brief Resolve file conflict
     * @param file_path Original file path
     * @return Resolved file path (may be modified to avoid conflict)
     */
    std::string resolve_file_conflict(const std::string& file_path);

    /**
     * @brief Generate metadata content for artwork
     * @param artwork Artwork information
     * @return Metadata content as string
     */
    std::string generate_metadata_content(const ArtworkInfo& artwork);

    /**
     * @brief Save metadata file for artwork
     * @param artwork Artwork information
     * @param directory_path Directory to save metadata
     * @return true if metadata saved successfully
     */
    bool save_metadata_file(const ArtworkInfo& artwork, const std::string& directory_path);

    /**
     * @brief Save novel content to file
     * @param artwork Novel artwork information
     * @param file_path File path to save
     * @return true if novel saved successfully
     */
    bool save_novel_file(const ArtworkInfo& artwork, const std::string& file_path);

    /**
     * @brief Check if file already exists and should be skipped
     * @param file_path File path to check
     * @return true if file should be skipped
     */
    bool should_skip_file(const std::string& file_path);

    /**
     * @brief Get last error message
     * @return Error message string
     */
    std::string get_last_error() const;

    /**
     * @brief Get storage statistics
     * @return Map of statistic name to value
     */
    std::map<std::string, int> get_statistics() const;

private:
    /**
     * @brief Generate filename from template
     * @param artwork Artwork information
     * @param page_index Page index (-1 for single page)
     * @param is_metadata True if this is for metadata file
     * @return Generated filename
     */
    std::string generate_filename(const ArtworkInfo& artwork, 
                                 int page_index = -1,
                                 bool is_metadata = false);

    /**
     * @brief Get file extension for artwork
     * @param artwork Artwork information
     * @param page_index Page index for multi-page artworks
     * @return File extension (including dot)
     */
    std::string get_file_extension(const ArtworkInfo& artwork, int page_index = -1);

    /**
     * @brief Generate unique filename to avoid conflicts
     * @param base_path Base file path
     * @return Unique file path
     */
    std::string generate_unique_filename(const std::string& base_path);

    /**
     * @brief Validate and sanitize path
     * @param path Input path
     * @return Sanitized path
     */
    std::string sanitize_path(const std::string& path);

    /**
     * @brief Check if path length is within system limits
     * @param path Path to check
     * @return true if path length is acceptable
     */
    bool is_path_length_valid(const std::string& path);

    /**
     * @brief Truncate path if too long
     * @param path Input path
     * @return Truncated path
     */
    std::string truncate_path_if_needed(const std::string& path);

    std::string output_dir_;
    PathTemplate path_template_;
    ConflictStrategy conflict_strategy_;
    std::string last_error_;
    
    // Statistics
    mutable std::mutex stats_mutex_;
    int files_created_;
    int directories_created_;
    int conflicts_resolved_;
    int metadata_files_created_;
};

/**
 * @brief Path template validator
 * 
 * Utility class to validate and test path templates before use.
 */
class PathTemplateValidator {
public:
    /**
     * @brief Validate path template
     * @param template_str Template string to validate
     * @return true if template is valid
     */
    static bool validate_template(const std::string& template_str);

    /**
     * @brief Get validation errors
     * @param template_str Template string to validate
     * @return Vector of error messages
     */
    static std::vector<std::string> get_validation_errors(const std::string& template_str);

    /**
     * @brief Get supported template variables
     * @return Vector of supported variable names
     */
    static std::vector<std::string> get_supported_variables();

    /**
     * @brief Test template with sample data
     * @param template_str Template string
     * @return Generated path using sample data
     */
    static std::string test_template(const std::string& template_str);

private:
    /**
     * @brief Extract variables from template
     * @param template_str Template string
     * @return Vector of variable names found in template
     */
    static std::vector<std::string> extract_variables(const std::string& template_str);

    /**
     * @brief Check if variable is supported
     * @param variable Variable name
     * @return true if variable is supported
     */
    static bool is_supported_variable(const std::string& variable);
};

} // namespace pixiv
