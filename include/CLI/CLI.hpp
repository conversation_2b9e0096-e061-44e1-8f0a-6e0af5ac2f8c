#pragma once

// Simplified CLI11 replacement for basic argument parsing
#include <string>
#include <vector>
#include <map>
#include <functional>
#include <stdexcept>
#include <algorithm>

namespace CLI {

class ParseError : public std::runtime_error {
public:
    ParseError(const std::string& msg) : std::runtime_error(msg) {}
};

class App {
public:
    App(const std::string& description = "") : description_(description) {}
    
    App* add_option(const std::string& name, std::string& var, const std::string& desc = "") {
        string_options_[name] = &var;
        descriptions_[name] = desc;
        return this;
    }
    
    App* add_option(const std::string& name, int& var, const std::string& desc = "") {
        int_options_[name] = &var;
        descriptions_[name] = desc;
        return this;
    }
    
    App* add_flag(const std::string& name, bool& var, const std::string& desc = "") {
        bool_options_[name] = &var;
        descriptions_[name] = desc;
        return this;
    }
    
    App* check(std::function<bool(const std::string&)> validator) {
        (void)validator; // Suppress unused parameter warning
        // Simplified - just return this for chaining
        return this;
    }

    App* add_option_group(const std::string& name) {
        (void)name; // Suppress unused parameter warning
        // Simplified - just return this
        return this;
    }

    App* require_option(int min, int max) {
        (void)min; (void)max; // Suppress unused parameter warnings
        // Simplified - just return this
        return this;
    }
    
    void parse(int argc, char* argv[]) {
        // Very basic parsing - just look for known options
        for (int i = 1; i < argc; ++i) {
            std::string arg = argv[i];
            
            if (arg == "-h" || arg == "--help") {
                throw ParseError("Help requested");
            }
            
            // Look for string options
            for (const auto& opt : string_options_) {
                if (arg == opt.first || arg.find(opt.first + "=") == 0) {
                    if (arg.find("=") != std::string::npos) {
                        *opt.second = arg.substr(arg.find("=") + 1);
                    } else if (i + 1 < argc) {
                        *opt.second = argv[++i];
                    }
                    break;
                }
            }
            
            // Look for int options
            for (const auto& opt : int_options_) {
                if (arg == opt.first || arg.find(opt.first + "=") == 0) {
                    std::string value;
                    if (arg.find("=") != std::string::npos) {
                        value = arg.substr(arg.find("=") + 1);
                    } else if (i + 1 < argc) {
                        value = argv[++i];
                    }
                    try {
                        *opt.second = std::stoi(value);
                    } catch (...) {
                        throw ParseError("Invalid integer value for " + opt.first);
                    }
                    break;
                }
            }
            
            // Look for bool flags
            for (const auto& opt : bool_options_) {
                if (arg == opt.first) {
                    *opt.second = true;
                    break;
                }
            }
        }
    }
    
    std::string help() const {
        std::string help_text = description_ + "\n\nOptions:\n";
        for (const auto& desc : descriptions_) {
            help_text += "  " + desc.first + "\t" + desc.second + "\n";
        }
        return help_text;
    }
    
    std::string exit(const ParseError& e) {
        return std::string("Error: ") + e.what();
    }
    
private:
    std::string description_;
    std::map<std::string, std::string*> string_options_;
    std::map<std::string, int*> int_options_;
    std::map<std::string, bool*> bool_options_;
    std::map<std::string, std::string> descriptions_;
};

// Helper functions
namespace detail {
    template<typename T>
    auto IsMember(const std::vector<T>& allowed) {
        return [allowed](const T& value) {
            return std::find(allowed.begin(), allowed.end(), value) != allowed.end();
        };
    }
}

template<typename T>
auto IsMember(const std::vector<T>& allowed) {
    return detail::IsMember(allowed);
}

// Convenience function for string literals
inline auto IsMember(std::initializer_list<const char*> allowed) {
    std::vector<std::string> vec;
    for (const char* item : allowed) {
        vec.emplace_back(item);
    }
    return detail::IsMember(vec);
}

} // namespace CLI
