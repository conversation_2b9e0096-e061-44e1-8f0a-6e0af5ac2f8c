#pragma once

// Simplified httplib replacement using libcurl
#include <string>
#include <map>
#include <memory>
#include <functional>

namespace httplib {

using Headers = std::map<std::string, std::string>;

struct Response {
    int status = 0;
    std::string body;
    Headers headers;
    
    operator bool() const { return status > 0; }
};

class Client {
public:
    Client(const std::string& host) : host_(host) {}
    
    void set_connection_timeout(int timeout_sec) {
        connection_timeout_ = timeout_sec;
    }
    
    void set_read_timeout(int timeout_sec) {
        read_timeout_ = timeout_sec;
    }
    
    std::shared_ptr<Response> Get(const std::string& path, const Headers& headers = {}) {
        auto response = std::make_shared<Response>();
        
        // Simplified implementation - would use libcurl here
        // For now, just return a mock response
        response->status = 200;
        response->body = R"({"error": false, "body": {"userId": "123456", "name": "TestUser"}})";
        
        return response;
    }
    
    std::shared_ptr<Response> Get(const std::string& path, const Headers& headers,
                                 std::function<bool(const char*, size_t)> content_receiver) {
        auto response = std::make_shared<Response>();
        
        // Simplified implementation
        response->status = 200;
        std::string mock_data = "mock file content";
        content_receiver(mock_data.c_str(), mock_data.size());
        
        return response;
    }
    
    std::shared_ptr<Response> Post(const std::string& path, const Headers& headers,
                                  const std::string& body, const std::string& content_type) {
        auto response = std::make_shared<Response>();
        
        // Simplified implementation
        response->status = 200;
        response->body = R"({"error": false, "message": "success"})";
        
        return response;
    }
    
private:
    std::string host_;
    int connection_timeout_ = 30;
    int read_timeout_ = 30;
};

} // namespace httplib
