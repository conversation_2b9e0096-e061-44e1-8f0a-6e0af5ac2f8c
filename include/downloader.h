#pragma once

#include "types.h"
#include "api.h"
#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <thread>
#include <atomic>

namespace pixiv {

/**
 * @brief Download result structure
 */
struct DownloadResult {
    bool success;
    std::string error_message;
    std::string local_path;
    size_t bytes_downloaded;
    std::chrono::milliseconds duration;
};

/**
 * @brief Abstract downloader interface
 */
class Downloader {
public:
    virtual ~Downloader() = default;

    /**
     * @brief Download a single file
     * @param task Download task
     * @param progress_callback Progress callback function
     * @return Download result
     */
    virtual DownloadResult download_file(const DownloadTask& task,
                                       std::function<void(size_t, size_t)> progress_callback = nullptr) = 0;

    /**
     * @brief Download multiple files
     * @param tasks Vector of download tasks
     * @param progress_callback Progress callback function
     * @return Vector of download results
     */
    virtual std::vector<DownloadResult> download_files(const std::vector<DownloadTask>& tasks,
                                                      std::function<void(const DownloadProgress&)> progress_callback = nullptr) = 0;

    /**
     * @brief Cancel ongoing downloads
     */
    virtual void cancel() = 0;

    /**
     * @brief Check if downloader is busy
     * @return true if downloading
     */
    virtual bool is_busy() const = 0;
};

/**
 * @brief Direct HTTP downloader implementation
 */
class DirectDownloader : public Downloader {
public:
    /**
     * @brief Constructor
     * @param http_client HTTP client instance
     */
    explicit DirectDownloader(std::shared_ptr<HttpClient> http_client);

    /**
     * @brief Destructor
     */
    ~DirectDownloader() override;

    DownloadResult download_file(const DownloadTask& task,
                               std::function<void(size_t, size_t)> progress_callback = nullptr) override;

    std::vector<DownloadResult> download_files(const std::vector<DownloadTask>& tasks,
                                             std::function<void(const DownloadProgress&)> progress_callback = nullptr) override;

    void cancel() override;
    bool is_busy() const override;

private:
    std::shared_ptr<HttpClient> http_client_;
    std::atomic<bool> cancelled_;
    std::atomic<bool> busy_;
};

/**
 * @brief Aria2c command-line downloader implementation
 */
class Aria2cDownloader : public Downloader {
public:
    /**
     * @brief Constructor
     */
    Aria2cDownloader();

    /**
     * @brief Destructor
     */
    ~Aria2cDownloader() override;

    DownloadResult download_file(const DownloadTask& task,
                               std::function<void(size_t, size_t)> progress_callback = nullptr) override;

    std::vector<DownloadResult> download_files(const std::vector<DownloadTask>& tasks,
                                             std::function<void(const DownloadProgress&)> progress_callback = nullptr) override;

    void cancel() override;
    bool is_busy() const override;

private:
    /**
     * @brief Create aria2c input file
     * @param tasks Download tasks
     * @return Path to input file
     */
    std::string create_input_file(const std::vector<DownloadTask>& tasks);

    /**
     * @brief Execute aria2c command
     * @param input_file Path to input file
     * @param progress_callback Progress callback
     * @return true if successful
     */
    bool execute_aria2c(const std::string& input_file,
                       std::function<void(const DownloadProgress&)> progress_callback);

    std::atomic<bool> cancelled_;
    std::atomic<bool> busy_;
};

/**
 * @brief Aria2 RPC downloader implementation
 */
class Aria2RpcDownloader : public Downloader {
public:
    /**
     * @brief Constructor
     * @param rpc_config Aria2 RPC configuration
     */
    explicit Aria2RpcDownloader(const Aria2Config& rpc_config);

    /**
     * @brief Destructor
     */
    ~Aria2RpcDownloader() override;

    DownloadResult download_file(const DownloadTask& task,
                               std::function<void(size_t, size_t)> progress_callback = nullptr) override;

    std::vector<DownloadResult> download_files(const std::vector<DownloadTask>& tasks,
                                             std::function<void(const DownloadProgress&)> progress_callback = nullptr) override;

    void cancel() override;
    bool is_busy() const override;

private:
    /**
     * @brief Connect to Aria2 RPC server
     * @return true if connection successful
     */
    bool connect();

    /**
     * @brief Disconnect from Aria2 RPC server
     */
    void disconnect();

    /**
     * @brief Add download to Aria2
     * @param task Download task
     * @return Download GID, empty if failed
     */
    std::string add_download(const DownloadTask& task);

    /**
     * @brief Get download status
     * @param gid Download GID
     * @return Download status info
     */
    std::map<std::string, std::string> get_download_status(const std::string& gid);

    /**
     * @brief Wait for downloads to complete
     * @param gids Vector of download GIDs
     * @param progress_callback Progress callback
     * @return Vector of download results
     */
    std::vector<DownloadResult> wait_for_downloads(const std::vector<std::string>& gids,
                                                  std::function<void(const DownloadProgress&)> progress_callback);

    Aria2Config config_;
    std::atomic<bool> connected_;
    std::atomic<bool> cancelled_;
    std::atomic<bool> busy_;
    // WebSocket connection implementation details would go here
};

/**
 * @brief Download manager with producer-consumer pattern
 * 
 * This class manages the download process using a producer-consumer pattern
 * where metadata fetching and downloading happen concurrently.
 */
class DownloadManager {
public:
    /**
     * @brief Constructor
     * @param downloader Downloader implementation
     * @param concurrency Number of concurrent download threads
     */
    DownloadManager(std::unique_ptr<Downloader> downloader, int concurrency = 4);

    /**
     * @brief Destructor
     */
    ~DownloadManager();

    /**
     * @brief Start download process
     * @param artworks Vector of artworks to download
     * @param progress_callback Progress callback function
     * @return true if all downloads successful
     */
    bool start_download(const std::vector<ArtworkInfo>& artworks,
                       std::function<void(const DownloadProgress&)> progress_callback = nullptr);

    /**
     * @brief Cancel ongoing downloads
     */
    void cancel();

    /**
     * @brief Check if download manager is busy
     * @return true if downloading
     */
    bool is_busy() const;

    /**
     * @brief Get download statistics
     * @return Download progress information
     */
    DownloadProgress get_progress() const;

private:
    /**
     * @brief Producer thread function
     * Generates download tasks from artwork information
     */
    void producer_thread(const std::vector<ArtworkInfo>& artworks);

    /**
     * @brief Consumer thread function
     * Processes download tasks from the queue
     */
    void consumer_thread();

    /**
     * @brief Generate download tasks for an artwork
     * @param artwork Artwork information
     * @return Vector of download tasks
     */
    std::vector<DownloadTask> generate_download_tasks(const ArtworkInfo& artwork);

    std::unique_ptr<Downloader> downloader_;
    int concurrency_;
    
    std::queue<DownloadTask> task_queue_;
    std::mutex queue_mutex_;
    std::condition_variable queue_cv_;
    
    std::vector<std::thread> worker_threads_;
    std::atomic<bool> cancelled_;
    std::atomic<bool> busy_;
    std::atomic<bool> producer_finished_;
    
    DownloadProgress progress_;
    std::mutex progress_mutex_;
    
    std::function<void(const DownloadProgress&)> progress_callback_;
};

/**
 * @brief Factory function to create downloader
 * @param method Download method
 * @param http_client HTTP client (for direct download)
 * @param aria2_config Aria2 configuration (for RPC download)
 * @return Unique pointer to downloader implementation
 */
std::unique_ptr<Downloader> create_downloader(DownloadMethod method,
                                             std::shared_ptr<HttpClient> http_client = nullptr,
                                             const Aria2Config& aria2_config = {});

} // namespace pixiv
